#!/usr/bin/env python3
"""
编码检测脚本

专门检测《神秘复苏.txt》的真实编码格式
"""

import os
from pathlib import Path


def analyze_file_bytes(file_path, sample_size=1000):
    """
    分析文件的字节特征
    
    Args:
        file_path: 文件路径
        sample_size: 采样大小
    """
    print(f"📊 分析文件字节特征: {file_path}")
    
    try:
        with open(file_path, 'rb') as f:
            data = f.read(sample_size)
        
        print(f"文件大小: {Path(file_path).stat().st_size:,} bytes")
        print(f"采样大小: {len(data)} bytes")
        
        # 分析字节分布
        byte_counts = {}
        for byte in data:
            byte_counts[byte] = byte_counts.get(byte, 0) + 1
        
        # 显示最常见的字节
        print(f"\n🔢 最常见的字节值 (前10个):")
        sorted_bytes = sorted(byte_counts.items(), key=lambda x: x[1], reverse=True)
        for i, (byte_val, count) in enumerate(sorted_bytes[:10]):
            percentage = count / len(data) * 100
            print(f"  {i+1:2d}. 0x{byte_val:02X} ({byte_val:3d}) - {count:4d}次 ({percentage:5.1f}%)")
        
        # 检查BOM
        print(f"\n🏷️ BOM检查:")
        if data.startswith(b'\xEF\xBB\xBF'):
            print("  ✅ 检测到UTF-8 BOM")
        elif data.startswith(b'\xFF\xFE'):
            print("  ✅ 检测到UTF-16 LE BOM")
        elif data.startswith(b'\xFE\xFF'):
            print("  ✅ 检测到UTF-16 BE BOM")
        else:
            print("  ❌ 未检测到BOM")
        
        # 分析字节范围
        ascii_count = sum(1 for b in data if 0 <= b <= 127)
        high_byte_count = sum(1 for b in data if b > 127)
        
        print(f"\n📈 字节范围分析:")
        print(f"  ASCII字符 (0-127): {ascii_count} ({ascii_count/len(data)*100:.1f}%)")
        print(f"  高位字节 (128-255): {high_byte_count} ({high_byte_count/len(data)*100:.1f}%)")
        
        return data
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None


def test_encoding_methods(file_path, data_sample):
    """
    测试不同的编码方法
    
    Args:
        file_path: 文件路径
        data_sample: 数据样本
    """
    print(f"\n🧪 测试不同编码方法:")
    
    encodings = [
        'utf-8', 'utf-8-sig', 'gbk', 'gb2312', 'gb18030', 
        'big5', 'latin1', 'cp936', 'ascii'
    ]
    
    results = []
    
    for encoding in encodings:
        try:
            # 尝试解码样本
            decoded = data_sample.decode(encoding, errors='ignore')
            
            # 统计中文字符
            chinese_chars = sum(1 for char in decoded if '\u4e00' <= char <= '\u9fff')
            total_chars = len(decoded.strip())
            chinese_ratio = chinese_chars / total_chars if total_chars > 0 else 0
            
            # 检查是否有乱码特征
            has_garbled = any(bad_char in decoded[:500] for bad_char in ['锟斤拷', '�', '\ufffd'])
            
            # 计算可打印字符比例
            printable_chars = sum(1 for char in decoded if char.isprintable() or char in '\n\r\t')
            printable_ratio = printable_chars / len(decoded) if len(decoded) > 0 else 0
            
            results.append({
                'encoding': encoding,
                'chinese_ratio': chinese_ratio,
                'printable_ratio': printable_ratio,
                'has_garbled': has_garbled,
                'sample_text': decoded[:100].replace('\n', '\\n').replace('\r', '\\r')
            })
            
        except Exception as e:
            results.append({
                'encoding': encoding,
                'error': str(e)
            })
    
    # 显示结果
    print(f"\n📋 编码测试结果:")
    print("-" * 80)
    print(f"{'编码':<12} {'中文比例':<8} {'可打印比例':<10} {'乱码':<6} {'样本文本'}")
    print("-" * 80)
    
    for result in results:
        if 'error' in result:
            print(f"{result['encoding']:<12} ❌ 解码失败: {result['error']}")
        else:
            garbled_status = "❌" if result['has_garbled'] else "✅"
            sample_text = result['sample_text'][:40] + "..." if len(result['sample_text']) > 40 else result['sample_text']
            
            print(f"{result['encoding']:<12} {result['chinese_ratio']*100:>6.1f}% {result['printable_ratio']*100:>8.1f}% {garbled_status:>6} {sample_text}")
    
    # 推荐最佳编码
    print(f"\n🎯 编码推荐:")
    valid_results = [r for r in results if 'error' not in r and not r['has_garbled']]
    
    if valid_results:
        # 按中文字符比例排序
        best_encoding = max(valid_results, key=lambda x: x['chinese_ratio'])
        print(f"  推荐编码: {best_encoding['encoding']}")
        print(f"  中文字符比例: {best_encoding['chinese_ratio']*100:.1f}%")
        print(f"  可打印字符比例: {best_encoding['printable_ratio']*100:.1f}%")
    else:
        print("  ❌ 未找到合适的编码")


def try_chardet_detection(file_path):
    """
    尝试使用chardet进行自动检测
    
    Args:
        file_path: 文件路径
    """
    try:
        import chardet
        
        print(f"\n🔍 chardet自动检测:")
        
        with open(file_path, 'rb') as f:
            raw_data = f.read(10000)  # 读取前10KB
        
        result = chardet.detect(raw_data)
        
        print(f"  检测结果: {result['encoding']}")
        print(f"  置信度: {result['confidence']:.2%}")
        print(f"  语言: {result.get('language', 'unknown')}")
        
        return result['encoding']
        
    except ImportError:
        print(f"\n⚠️ chardet库未安装，跳过自动检测")
        return None
    except Exception as e:
        print(f"\n❌ chardet检测失败: {e}")
        return None


def detailed_content_analysis(file_path, encoding):
    """
    详细分析文件内容
    
    Args:
        file_path: 文件路径
        encoding: 使用的编码
    """
    try:
        print(f"\n📖 详细内容分析 (使用编码: {encoding}):")
        
        with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
            content = f.read(2000)  # 读取前2000字符
        
        # 统计各种字符
        chinese_chars = sum(1 for char in content if '\u4e00' <= char <= '\u9fff')
        english_chars = sum(1 for char in content if char.isalpha() and ord(char) < 128)
        digits = sum(1 for char in content if char.isdigit())
        punctuation = sum(1 for char in content if char in '，。！？；：""''（）【】《》')
        spaces = sum(1 for char in content if char in ' \t\n\r')
        
        total_chars = len(content)
        
        print(f"  总字符数: {total_chars}")
        print(f"  中文字符: {chinese_chars} ({chinese_chars/total_chars*100:.1f}%)")
        print(f"  英文字符: {english_chars} ({english_chars/total_chars*100:.1f}%)")
        print(f"  数字字符: {digits} ({digits/total_chars*100:.1f}%)")
        print(f"  中文标点: {punctuation} ({punctuation/total_chars*100:.1f}%)")
        print(f"  空白字符: {spaces} ({spaces/total_chars*100:.1f}%)")
        
        # 显示前几行内容
        print(f"\n📄 内容预览 (前5行):")
        lines = content.split('\n')[:5]
        for i, line in enumerate(lines, 1):
            line_preview = line.strip()[:60] + "..." if len(line.strip()) > 60 else line.strip()
            if line_preview:
                print(f"  {i}: {line_preview}")
        
    except Exception as e:
        print(f"❌ 内容分析失败: {e}")


def main():
    """主函数"""
    print("🔍 《神秘复苏.txt》编码检测工具")
    print("=" * 60)
    
    file_path = "uploads/神秘复苏.txt"
    
    if not Path(file_path).exists():
        print(f"❌ 文件不存在: {file_path}")
        return 1
    
    # 1. 分析文件字节特征
    data_sample = analyze_file_bytes(file_path)
    if not data_sample:
        return 1
    
    # 2. 测试不同编码方法
    test_encoding_methods(file_path, data_sample)
    
    # 3. 尝试chardet自动检测
    detected_encoding = try_chardet_detection(file_path)
    
    # 4. 详细内容分析
    if detected_encoding:
        detailed_content_analysis(file_path, detected_encoding)
    else:
        # 使用最可能的编码进行分析
        detailed_content_analysis(file_path, 'gbk')
    
    print(f"\n🎯 总结:")
    print("1. 查看上述分析结果，找到中文字符比例最高且无乱码的编码")
    print("2. 如果所有编码都有问题，说明原文件可能已损坏")
    print("3. 建议重新获取原始文件或使用其他测试文件")
    
    return 0


if __name__ == "__main__":
    exit(main())
