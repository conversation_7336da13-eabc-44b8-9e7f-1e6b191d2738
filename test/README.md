# 小说解析功能测试框架

## 🎯 项目概述

这是一个专为小说解析功能设计的控制台式测试框架，可以绕过FastAPI接口直接测试核心功能模块。通过模拟真实的使用场景，提供了从文件上传到分步解析的完整测试流程。

## 📁 文件结构

```
test/
├── console.py              # 主控制台脚本
├── test_utils.py           # 测试工具函数库
├── step1_chapter_split.py  # 章节分割测试
├── step2_content_clean.py  # 内容清洗测试
└── README.md              # 本文档
```

## 🚀 快速开始

### 启动控制台
```bash
cd /Users/<USER>/pyProject/tinytalesstudionew
python test/console.py
```

### 完整测试流程
1. **创建上传任务** (1.1) → 输入文件名
2. **完成上传** (1.3) → 输入上传会话ID
3. **创建解析任务** (2.1) → 输入会话ID、标题、作者
4. **章节分割测试** (3.1) → 选择小说文档
5. **内容清洗测试** (3.2) → 选择已分割的小说
6. **查看结果** (4.1) → 查看数据库状态

## 🎮 控制台功能

### 1. 上传管理
- **1.1 创建上传任务** - 创建新的文件上传会话
- **1.2 检查上传状态** - 查看所有上传会话状态
- **1.3 完成上传** - 标记上传会话为完成状态
- **1.4 删除上传任务** - 删除指定上传任务

### 2. 解析任务管理
- **2.1 创建解析任务** - 基于上传会话创建小说文档
- **2.2 检查解析状态** - 查看解析任务状态
- **2.3 重置解析状态** - 重置小说解析状态

### 3. 分步骤测试
- **3.1 章节分割测试** - 独立测试章节分割功能
- **3.2 内容清洗测试** - 独立测试内容清洗功能
- **3.3 实体提取测试** - 待实现
- **3.4 关系分析测试** - 待实现

### 4. 数据管理
- **4.1 查看数据库状态** - 显示详细的数据库统计信息
- **4.2 清理测试数据** - 清理所有测试数据
- **4.3 重置所有数据** - 完全重置数据库

## 🧪 分步测试脚本

### 章节分割测试 (`step1_chapter_split.py`)
- 从数据库选择小说文档
- 执行章节分割处理
- 显示分割结果统计
- 更新解析状态

**测试结果示例：**
```
✅ 章节分割成功完成
⏱️ 耗时: 0:00:01.234567
📊 分割结果: 共 102 个章节
总字数: 739,734
平均字数: 7252
```

### 内容清洗测试 (`step2_content_clean.py`)
- 选择已完成章节分割的小说
- 执行内容清洗处理
- 显示清洗前后对比
- 更新处理状态

**测试结果示例：**
```
✅ 内容清洗成功完成
⏱️ 耗时: 0:00:00.567890
📊 处理结果: 共处理 102 个章节
处理进度: 100.0%
```

## 🛠️ 核心功能

### TestDataManager 类
位于 `test_utils.py`，提供以下功能：

- **数据库会话管理** - 自动管理数据库连接
- **测试用户管理** - 创建和管理测试用户
- **上传会话管理** - 模拟文件上传流程
- **小说文档管理** - 创建和管理解析任务
- **状态查看** - 详细的数据库状态显示
- **数据清理** - 安全的测试数据清理

### 关键特性

1. **真实环境模拟** - 完整模拟从上传到解析的整个流程
2. **分步验证** - 每个解析步骤都可以独立测试和验证
3. **状态管理** - 实时跟踪和管理解析状态
4. **错误处理** - 完善的异常处理和错误提示
5. **数据安全** - 安全的测试数据管理和清理

## 📊 测试验证

### 已验证功能
- ✅ **ChapterSplitter** - 章节分割功能
- ✅ **ContentCleaner** - 内容清洗功能
- ✅ **数据库操作** - 完整的CRUD操作
- ✅ **状态管理** - 解析状态跟踪
- ✅ **文件处理** - 大文件分块读取

### 测试数据
- **测试文件**: `uploads/西游记.txt`
- **章节数量**: 102个章节
- **总字数**: 739,734字
- **平均章节字数**: 7,252字

## 💡 使用建议

### 开发调试
1. 使用控制台快速创建测试环境
2. 运行分步测试脚本验证特定功能
3. 查看数据库状态了解处理结果
4. 使用数据清理功能重置环境

### 功能验证
1. 修改代码后运行对应的测试脚本
2. 对比测试结果验证修改效果
3. 使用重置功能测试不同场景

### 性能测试
1. 记录各步骤的执行时间
2. 监控内存使用情况
3. 测试不同大小的文件

## 🔧 扩展开发

### 添加新的测试步骤
1. 创建新的 `stepX_xxx.py` 脚本
2. 在控制台菜单中添加对应选项
3. 实现测试逻辑和结果验证

### 自定义测试数据
1. 修改 `test_utils.py` 中的文件路径
2. 添加新的测试文件支持
3. 扩展数据管理功能

## 🎉 总结

这个测试框架提供了：

- **完整的测试环境** - 从数据准备到结果验证
- **灵活的测试方式** - 控制台交互 + 独立脚本
- **真实的使用场景** - 模拟完整的业务流程
- **便捷的调试工具** - 详细的状态显示和错误提示

通过这个框架，您可以：
- 快速验证功能修改
- 独立测试特定模块
- 调试复杂的解析流程
- 验证不同的测试场景

现在您可以高效地进行小说解析功能的开发和测试！
