#!/usr/bin/env python3
"""
步骤2：内容清洗测试

基于已分割的章节进行内容清洗测试
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "backend"))

from datetime import datetime
from loguru import logger

# 导入测试工具
from test_utils import test_manager

# 导入业务模块
from backend.app.core.database.models import NovelDocument, NovelChapter
from backend.app.core.novel_parser.content_clean import ContentCleaner


def select_novel_document():
    """选择要测试的小说文档"""
    db = test_manager.get_db_session()
    
    # 获取已完成章节分割的小说文档
    novels = db.query(NovelDocument).filter(
        NovelDocument.step_chapter_split == True
    ).all()
    
    if not novels:
        print("❌ 没有找到已完成章节分割的小说文档")
        print("💡 请先运行章节分割测试: python test/step1_chapter_split.py")
        return None
    
    print("\n📚 可用的小说文档 (已完成章节分割):")
    for i, novel in enumerate(novels, 1):
        chapter_count = db.query(NovelChapter).filter(
            NovelChapter.novel_document_id == novel.id
        ).count()
        clean_status = "✓ 已清洗" if novel.step_content_clean else "⏳ 未清洗"
        print(f"  {i}. ID: {novel.id} | 标题: {novel.title} | 章节数: {chapter_count} | {clean_status}")
    
    while True:
        try:
            choice = input(f"\n请选择小说 (1-{len(novels)}): ").strip()
            index = int(choice) - 1
            if 0 <= index < len(novels):
                return novels[index]
            else:
                print("❌ 选择超出范围，请重新输入")
        except ValueError:
            print("❌ 请输入有效的数字")


def show_chapter_content_preview(novel_document, limit=3):
    """显示章节内容预览"""
    db = test_manager.get_db_session()
    
    chapters = db.query(NovelChapter).filter(
        NovelChapter.novel_document_id == novel_document.id
    ).order_by(NovelChapter.chapter_index).limit(limit).all()
    
    if not chapters:
        print("📖 暂无章节内容")
        return
    
    print(f"\n📖 章节内容预览 (前{limit}章):")
    for chapter in chapters:
        print(f"\n第{chapter.chapter_index}章: {chapter.title}")
        print(f"字数: {chapter.word_count}")
        print(f"处理状态: {'✓ 已处理' if chapter.is_processed else '⏳ 未处理'}")
        
        # 显示内容片段
        content_preview = chapter.content[:200] + "..." if len(chapter.content) > 200 else chapter.content
        print(f"内容预览: {content_preview}")
        print("-" * 50)


def test_content_clean(novel_document):
    """测试内容清洗功能"""
    print(f"\n🧹 开始内容清洗测试")
    print(f"小说ID: {novel_document.id}")
    print(f"标题: {novel_document.title}")
    
    db = test_manager.get_db_session()
    
    # 检查章节数量
    chapters = db.query(NovelChapter).filter(
        NovelChapter.novel_document_id == novel_document.id
    ).all()
    
    if not chapters:
        print("❌ 没有找到章节数据，请先进行章节分割")
        return False
    
    print(f"📊 待处理章节数: {len(chapters)}")
    
    # 检查是否已经清洗过
    processed_count = sum(1 for chapter in chapters if chapter.is_processed)
    if processed_count > 0:
        print(f"⚠️ 发现已处理 {processed_count} 个章节")
        choice = input("是否重新清洗所有章节？(y/N): ").strip().lower()
        if choice == 'y':
            # 重置处理状态
            for chapter in chapters:
                chapter.is_processed = False
            db.commit()
            print("✓ 已重置章节处理状态")
        else:
            print("❌ 操作已取消")
            return False
    
    try:
        # 准备内容工厂函数（虽然内容清洗不需要原始文件，但保持接口一致）
        upload_session = novel_document.upload_session
        content_factory = test_manager.get_file_content_factory(upload_session.filename)
        
        # 创建内容清洗器
        cleaner = ContentCleaner(db)
        
        # 更新小说状态
        novel_document.parse_status = "processing"
        novel_document.current_step = "content_clean"
        db.commit()
        
        print("🚀 开始执行内容清洗...")
        start_time = datetime.now()
        
        # 执行清洗
        success = cleaner.process(novel_document, content_factory)
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        if success:
            # 检查结果
            processed_chapters = db.query(NovelChapter).filter(
                NovelChapter.novel_document_id == novel_document.id,
                NovelChapter.is_processed == True
            ).all()
            
            print(f"✅ 内容清洗成功完成")
            print(f"⏱️ 耗时: {duration}")
            print(f"📊 处理结果: 共处理 {len(processed_chapters)} 个章节")
            
            # 更新小说状态
            novel_document.parse_status = "processing"
            novel_document.current_step = "entity_extract"
            novel_document.parse_progress = 40.0
            novel_document.step_content_clean = True
            novel_document.step_content_clean_at = datetime.utcnow()
            db.commit()
            
            print(f"✓ 小说状态已更新，可以进行下一步：实体提取")
            return True
        
        else:
            print(f"❌ 内容清洗失败")
            
            # 更新失败状态
            novel_document.parse_status = "failed"
            novel_document.parse_error = "内容清洗失败"
            db.commit()
            
            return False
    
    except Exception as e:
        print(f"❌ 内容清洗异常: {e}")
        logger.exception("内容清洗测试异常")
        
        # 更新失败状态
        novel_document.parse_status = "failed"
        novel_document.parse_error = f"内容清洗异常: {str(e)}"
        db.commit()
        
        return False


def show_clean_statistics(novel_document):
    """显示清洗统计信息"""
    db = test_manager.get_db_session()
    
    chapters = db.query(NovelChapter).filter(
        NovelChapter.novel_document_id == novel_document.id
    ).all()
    
    if not chapters:
        print("📊 暂无章节数据")
        return
    
    processed_count = sum(1 for chapter in chapters if chapter.is_processed)
    unprocessed_count = len(chapters) - processed_count
    
    print(f"\n📊 内容清洗统计:")
    print(f"总章节数: {len(chapters)}")
    print(f"已处理: {processed_count}")
    print(f"未处理: {unprocessed_count}")
    print(f"处理进度: {processed_count/len(chapters)*100:.1f}%")
    
    if processed_count > 0:
        # 统计处理后的字数变化
        processed_chapters = [c for c in chapters if c.is_processed]
        total_words = sum(chapter.word_count for chapter in processed_chapters)
        avg_words = total_words / len(processed_chapters)
        
        print(f"已处理章节总字数: {total_words:,}")
        print(f"已处理章节平均字数: {avg_words:.0f}")


def main():
    """主函数"""
    print("🧹 内容清洗测试")
    print("="*50)
    
    try:
        # 选择小说文档
        novel_document = select_novel_document()
        if not novel_document:
            return 1
        
        # 显示当前状态
        show_clean_statistics(novel_document)
        show_chapter_content_preview(novel_document)
        
        # 确认执行
        print(f"\n准备对小说《{novel_document.title}》进行内容清洗测试")
        choice = input("是否继续？(Y/n): ").strip().lower()
        if choice == 'n':
            print("❌ 操作已取消")
            return 0
        
        # 执行测试
        success = test_content_clean(novel_document)
        
        if success:
            # 显示结果统计
            show_clean_statistics(novel_document)
            show_chapter_content_preview(novel_document)
            print(f"\n🎉 内容清洗测试完成！")
            print(f"💡 下一步可以运行: python test/step3_entity_extract.py")
            return 0
        else:
            print(f"\n❌ 内容清洗测试失败")
            return 1
    
    except KeyboardInterrupt:
        print(f"\n\n👋 用户中断，测试退出")
        return 1
    
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        logger.exception("内容清洗测试主函数异常")
        return 1
    
    finally:
        test_manager.close_db_session()


if __name__ == "__main__":
    exit(main())
