#!/usr/bin/env python3
"""
测试工具函数库

提供测试所需的基础功能和数据管理
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "backend"))

import uuid
from datetime import datetime
from loguru import logger

# 导入数据库相关
from backend.app.core.database.base import db_manager
from backend.app.core.database.models import (
    User, UploadSession, NovelDocument, NovelChapter, NovelEntity
)


class TestDataManager:
    """测试数据管理器"""
    
    def __init__(self):
        self.db = None
        self.test_user = None
        
    def get_db_session(self):
        """获取数据库会话"""
        if not self.db:
            self.db = next(db_manager.get_db())
        return self.db
    
    def close_db_session(self):
        """关闭数据库会话"""
        if self.db:
            self.db.close()
            self.db = None
    
    def get_or_create_test_user(self):
        """获取或创建测试用户"""
        db = self.get_db_session()
        
        user = db.query(User).filter(User.username == "test_user").first()
        if not user:
            user = User(
                username="test_user",
                email="<EMAIL>",
                hashed_password="test_password_hash",
                is_active=True
            )
            db.add(user)
            db.commit()
            db.refresh(user)
            print(f"✓ 创建测试用户: {user.username}")
        else:
            print(f"✓ 使用现有测试用户: {user.username}")
        
        self.test_user = user
        return user
    
    def create_upload_session(self, filename="西游记.txt"):
        """创建上传会话"""
        db = self.get_db_session()
        user = self.get_or_create_test_user()
        
        # 检查文件是否存在
        file_path = project_root / "uploads" / filename
        if not file_path.exists():
            print(f"❌ 文件不存在: {file_path}")
            return None
        
        file_size = file_path.stat().st_size
        
        upload_session = UploadSession(
            filename=filename,
            file_size=file_size,
            chunk_size=1024*1024,  # 1MB
            total_chunks=1,
            uploaded_chunks=0,  # 初始为0，模拟未完成状态
            temp_dir=str(project_root / "uploads"),
            is_completed=False,  # 初始为未完成
            user_id=user.id
        )
        
        db.add(upload_session)
        db.commit()
        db.refresh(upload_session)
        
        print(f"✓ 创建上传会话: ID={upload_session.id}, 文件={filename}")
        return upload_session
    
    def complete_upload_session(self, session_id):
        """完成上传会话"""
        db = self.get_db_session()
        
        session = db.query(UploadSession).filter(UploadSession.id == session_id).first()
        if not session:
            print(f"❌ 找不到上传会话: {session_id}")
            return False
        
        session.uploaded_chunks = session.total_chunks
        session.is_completed = True
        db.commit()
        
        print(f"✓ 完成上传会话: ID={session_id}")
        return True
    
    def create_novel_document(self, upload_session_id, title=None, author=None):
        """创建小说文档"""
        db = self.get_db_session()
        
        upload_session = db.query(UploadSession).filter(
            UploadSession.id == upload_session_id
        ).first()
        
        if not upload_session:
            print(f"❌ 找不到上传会话: {upload_session_id}")
            return None
        
        if not upload_session.is_completed:
            print(f"❌ 上传会话未完成: {upload_session_id}")
            return None
        
        # 检查是否已存在
        existing = db.query(NovelDocument).filter(
            NovelDocument.upload_session_id == upload_session_id
        ).first()
        
        if existing:
            print(f"✓ 使用现有小说文档: ID={existing.id}")
            return existing
        
        novel_document = NovelDocument(
            upload_session_id=upload_session_id,
            title=title or upload_session.filename.rsplit(".", 1)[0],
            author=author or "测试作者",
            parse_status="pending",
            parse_progress=0.0,
            current_step="not_started",
            task_id=f"test_novel_{uuid.uuid4().hex[:8]}"
        )
        
        db.add(novel_document)
        db.commit()
        db.refresh(novel_document)
        
        print(f"✓ 创建小说文档: ID={novel_document.id}, 标题={novel_document.title}")
        return novel_document
    
    def get_file_content_factory(self, filename="西游记.txt"):
        """获取文件内容工厂函数"""
        file_path = project_root / "uploads" / filename
        
        def content_factory():
            if not file_path.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                yield content
        
        return content_factory
    
    def show_database_status(self):
        """显示数据库状态"""
        db = self.get_db_session()
        
        print("\n" + "="*50)
        print("📊 数据库状态")
        print("="*50)
        
        # 统计信息
        user_count = db.query(User).count()
        upload_count = db.query(UploadSession).count()
        novel_count = db.query(NovelDocument).count()
        chapter_count = db.query(NovelChapter).count()
        entity_count = db.query(NovelEntity).count()
        
        print(f"用户数量: {user_count}")
        print(f"上传会话数量: {upload_count}")
        print(f"小说文档数量: {novel_count}")
        print(f"章节数量: {chapter_count}")
        print(f"实体数量: {entity_count}")
        
        # 详细信息
        if upload_count > 0:
            print(f"\n📁 上传会话详情:")
            sessions = db.query(UploadSession).all()
            for session in sessions:
                status = "✓ 已完成" if session.is_completed else "⏳ 进行中"
                print(f"  ID: {session.id} | {session.filename} | {status}")
        
        if novel_count > 0:
            print(f"\n📚 小说文档详情:")
            novels = db.query(NovelDocument).all()
            for novel in novels:
                chapter_count = db.query(NovelChapter).filter(
                    NovelChapter.novel_document_id == novel.id
                ).count()
                print(f"  ID: {novel.id} | {novel.title} | 状态: {novel.parse_status} | 章节: {chapter_count}")
    
    def clear_test_data(self):
        """清理测试数据"""
        db = self.get_db_session()
        
        print("🧹 开始清理测试数据...")
        
        # 删除章节
        chapter_count = db.query(NovelChapter).count()
        if chapter_count > 0:
            db.query(NovelChapter).delete()
            print(f"✓ 删除 {chapter_count} 个章节")
        
        # 删除实体
        entity_count = db.query(NovelEntity).count()
        if entity_count > 0:
            db.query(NovelEntity).delete()
            print(f"✓ 删除 {entity_count} 个实体")
        
        # 删除小说文档
        novel_count = db.query(NovelDocument).count()
        if novel_count > 0:
            db.query(NovelDocument).delete()
            print(f"✓ 删除 {novel_count} 个小说文档")
        
        # 删除上传会话
        upload_count = db.query(UploadSession).count()
        if upload_count > 0:
            db.query(UploadSession).delete()
            print(f"✓ 删除 {upload_count} 个上传会话")
        
        # 删除测试用户
        test_users = db.query(User).filter(User.username.like("test_%")).all()
        if test_users:
            for user in test_users:
                db.delete(user)
            print(f"✓ 删除 {len(test_users)} 个测试用户")
        
        db.commit()
        print("✅ 测试数据清理完成")
    
    def reset_novel_status(self, novel_id):
        """重置小说解析状态"""
        db = self.get_db_session()
        
        novel = db.query(NovelDocument).filter(NovelDocument.id == novel_id).first()
        if not novel:
            print(f"❌ 找不到小说文档: {novel_id}")
            return False
        
        novel.parse_status = "pending"
        novel.parse_progress = 0.0
        novel.current_step = "not_started"
        novel.parse_error = None
        novel.is_parsed = False
        novel.parse_started_at = None
        novel.parse_completed_at = None
        
        db.commit()
        print(f"✓ 重置小说状态: ID={novel_id}")
        return True


# 全局实例
test_manager = TestDataManager()
