#!/usr/bin/env python3
"""
步骤1：章节分割测试

从数据库获取小说文档，进行章节分割测试
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "backend"))

from datetime import datetime
from loguru import logger

# 导入测试工具
from test_utils import test_manager

# 导入业务模块
from backend.app.core.database.models import NovelDocument, NovelChapter
from backend.app.core.novel_parser.chapter_split import ChapterSplitter


def select_novel_document():
    """选择要测试的小说文档"""
    db = test_manager.get_db_session()
    
    # 获取所有小说文档
    novels = db.query(NovelDocument).filter(
        NovelDocument.upload_session_id.isnot(None)
    ).all()
    
    if not novels:
        print("❌ 没有找到可用的小说文档")
        print("💡 请先使用控制台创建上传任务和解析任务")
        return None
    
    print("\n📚 可用的小说文档:")
    for i, novel in enumerate(novels, 1):
        chapter_count = db.query(NovelChapter).filter(
            NovelChapter.novel_document_id == novel.id
        ).count()
        print(f"  {i}. ID: {novel.id} | 标题: {novel.title} | 状态: {novel.parse_status} | 章节数: {chapter_count}")
    
    while True:
        try:
            choice = input(f"\n请选择小说 (1-{len(novels)}): ").strip()
            index = int(choice) - 1
            if 0 <= index < len(novels):
                return novels[index]
            else:
                print("❌ 选择超出范围，请重新输入")
        except ValueError:
            print("❌ 请输入有效的数字")


def test_chapter_split(novel_document):
    """测试章节分割功能"""
    print(f"\n🔪 开始章节分割测试")
    print(f"小说ID: {novel_document.id}")
    print(f"标题: {novel_document.title}")
    print(f"当前状态: {novel_document.parse_status}")
    
    db = test_manager.get_db_session()
    
    # 检查是否已有章节
    existing_chapters = db.query(NovelChapter).filter(
        NovelChapter.novel_document_id == novel_document.id
    ).count()

    # 输出NovelChapter模型的所有属性和中文解释
    print("\n📋 NovelChapter模型属性详解:")
    print("=" * 60)
    print("id                  - 主键ID (自增整数)")
    print("novel_document_id   - 关联的小说文档ID (外键)")
    print("chapter_index       - 章节序号 (整数，表示第几章)")
    print("title               - 章节标题 (字符串，可为空)")
    print("content             - 章节内容 (文本，章节的完整内容)")
    print("word_count          - 字数统计 (整数，默认为0)")
    print("is_processed        - 是否已处理 (布尔值，默认False)")
    print("created_at          - 创建时间 (时间戳)")
    print("updated_at          - 更新时间 (时间戳，自动更新)")
    print("novel_document      - 关联关系 (指向NovelDocument对象)")
    print("=" * 60)

    if existing_chapters > 0:
        print(f"⚠️ 发现已存在 {existing_chapters} 个章节")
        choice = input("是否清除现有章节重新分割？(y/N): ").strip().lower()
        if choice == 'y':
            db.query(NovelChapter).filter(
                NovelChapter.novel_document_id == novel_document.id
            ).delete()
            db.commit()
            print("✓ 已清除现有章节")
        else:
            print("❌ 操作已取消")
            return False
    
    try:
        # 获取上传会话信息
        upload_session = novel_document.upload_session
        if not upload_session:
            print("❌ 找不到关联的上传会话")
            return False
        
        print(f"文件名: {upload_session.filename}")
        
        # 准备内容工厂函数
        content_factory = test_manager.get_file_content_factory(upload_session.filename)
        
        # 创建章节分割器
        splitter = ChapterSplitter(db)
        
        # 更新小说状态
        novel_document.parse_status = "processing"
        novel_document.current_step = "chapter_split"
        novel_document.parse_started_at = datetime.utcnow()
        db.commit()
        
        print("🚀 开始执行章节分割...")
        start_time = datetime.now()
        
        # 执行分割
        success = splitter.process(novel_document, content_factory)
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        if success:
            # 检查结果
            chapters = db.query(NovelChapter).filter(
                NovelChapter.novel_document_id == novel_document.id
            ).all()
            
            print(f"✅ 章节分割成功完成")
            print(f"⏱️ 耗时: {duration}")
            print(f"📊 分割结果: 共 {len(chapters)} 个章节")
            
            # 显示前几章信息
            if chapters:
                print(f"\n📖 前5章预览:")
                for i, chapter in enumerate(chapters[:5]):
                    title_preview = chapter.title[:40] + "..." if len(chapter.title) > 40 else chapter.title
                    print(f"  第{chapter.chapter_index}章: {title_preview} (字数: {chapter.word_count})")
            
            # 更新小说状态
            novel_document.parse_status = "processing"
            novel_document.current_step = "content_clean"
            novel_document.parse_progress = 20.0
            novel_document.step_chapter_split = True
            novel_document.step_chapter_split_at = datetime.utcnow()
            db.commit()
            
            print(f"✓ 小说状态已更新，可以进行下一步：内容清洗")
            return True
        
        else:
            print(f"❌ 章节分割失败")
            
            # 更新失败状态
            novel_document.parse_status = "failed"
            novel_document.parse_error = "章节分割失败"
            db.commit()
            
            return False
    
    except Exception as e:
        print(f"❌ 章节分割异常: {e}")
        logger.exception("章节分割测试异常")
        
        # 更新失败状态
        novel_document.parse_status = "failed"
        novel_document.parse_error = f"章节分割异常: {str(e)}"
        db.commit()
        
        return False


def show_chapter_statistics(novel_document):
    """显示章节统计信息"""
    db = test_manager.get_db_session()
    
    chapters = db.query(NovelChapter).filter(
        NovelChapter.novel_document_id == novel_document.id
    ).all()
    
    if not chapters:
        print("📊 暂无章节数据")
        return
    
    print(f"\n📊 章节统计信息:")
    print(f"总章节数: {len(chapters)}")
    
    # 字数统计
    total_words = sum(chapter.word_count for chapter in chapters)
    avg_words = total_words / len(chapters) if chapters else 0
    max_words = max(chapter.word_count for chapter in chapters) if chapters else 0
    min_words = min(chapter.word_count for chapter in chapters) if chapters else 0
    
    print(f"总字数: {total_words:,}")
    print(f"平均字数: {avg_words:.0f}")
    print(f"最大字数: {max_words:,}")
    print(f"最小字数: {min_words:,}")
    
    # 标题长度统计
    title_lengths = [len(chapter.title) for chapter in chapters if chapter.title]
    if title_lengths:
        avg_title_length = sum(title_lengths) / len(title_lengths)
        print(f"平均标题长度: {avg_title_length:.1f} 字符")


def main():
    """主函数"""
    print("🔪 章节分割测试")
    print("="*50)
    
    try:
        # 选择小说文档
        novel_document = select_novel_document()
        if not novel_document:
            return 1
        
        # 显示当前状态
        show_chapter_statistics(novel_document)
        
        # 确认执行
        print(f"\n准备对小说《{novel_document.title}》进行章节分割测试")
        choice = input("是否继续？(Y/n): ").strip().lower()
        if choice == 'n':
            print("❌ 操作已取消")
            return 0
        
        # 执行测试
        success = test_chapter_split(novel_document)
        
        if success:
            # 显示结果统计
            show_chapter_statistics(novel_document)
            print(f"\n🎉 章节分割测试完成！")
            print(f"💡 下一步可以运行: python test/step2_content_clean.py")
            return 0
        else:
            print(f"\n❌ 章节分割测试失败")
            return 1
    
    except KeyboardInterrupt:
        print(f"\n\n👋 用户中断，测试退出")
        return 1
    
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        logger.exception("章节分割测试主函数异常")
        return 1
    
    finally:
        test_manager.close_db_session()


if __name__ == "__main__":
    exit(main())
