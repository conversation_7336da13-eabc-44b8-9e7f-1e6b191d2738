#!/usr/bin/env python3
"""
小说解析测试控制台

提供交互式界面管理整个测试流程
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "backend"))

from test_utils import test_manager


def show_main_menu():
    """显示主菜单"""
    print("\n" + "="*60)
    print("🎯 小说解析测试控制台")
    print("="*60)
    print("1. 上传管理")
    print("   1.1 创建上传任务")
    print("   1.2 检查上传状态")
    print("   1.3 完成上传")
    print("   1.4 删除上传任务")
    print()
    print("2. 解析任务管理")
    print("   2.1 创建解析任务")
    print("   2.2 检查解析状态")
    print("   2.3 重置解析状态")
    print()
    print("3. 分步骤测试")
    print("   3.1 章节分割测试")
    print("   3.2 内容清洗测试")
    print("   3.3 实体提取测试")
    print("   3.4 关系分析测试")
    print()
    print("4. 数据管理")
    print("   4.1 查看数据库状态")
    print("   4.2 清理测试数据")
    print("   4.3 重置所有数据")
    print()
    print("5. 退出")
    print("="*60)


def handle_upload_management():
    """处理上传管理"""
    while True:
        print("\n📁 上传管理")
        print("1. 创建上传任务")
        print("2. 检查上传状态")
        print("3. 完成上传")
        print("4. 删除上传任务")
        print("0. 返回主菜单")
        
        choice = input("\n请选择操作: ").strip()
        
        if choice == "1":
            filename = input("请输入文件名 (默认: 神秘复苏.txt): ").strip()
            if not filename:
                filename = "神秘复苏.txt"
            
            session = test_manager.create_upload_session(filename)
            if session:
                print(f"✅ 上传任务创建成功，会话ID: {session.id}")
        
        elif choice == "2":
            test_manager.show_database_status()
        
        elif choice == "3":
            session_id = input("请输入上传会话ID: ").strip()
            try:
                session_id = int(session_id)
                if test_manager.complete_upload_session(session_id):
                    print("✅ 上传任务完成")
                else:
                    print("❌ 完成上传任务失败")
            except ValueError:
                print("❌ 请输入有效的会话ID")
        
        elif choice == "4":
            print("🚧 删除功能待实现")
        
        elif choice == "0":
            break
        
        else:
            print("❌ 无效选择，请重新输入")


def handle_parse_management():
    """处理解析任务管理"""
    while True:
        print("\n⚙️ 解析任务管理")
        print("1. 创建解析任务")
        print("2. 检查解析状态")
        print("3. 重置解析状态")
        print("0. 返回主菜单")
        
        choice = input("\n请选择操作: ").strip()
        
        if choice == "1":
            session_id = input("请输入上传会话ID: ").strip()
            title = input("请输入小说标题 (可选): ").strip()
            author = input("请输入作者 (可选): ").strip()
            
            try:
                session_id = int(session_id)
                novel = test_manager.create_novel_document(
                    session_id, 
                    title if title else None,
                    author if author else None
                )
                if novel:
                    print(f"✅ 解析任务创建成功，小说ID: {novel.id}")
                else:
                    print("❌ 创建解析任务失败")
            except ValueError:
                print("❌ 请输入有效的会话ID")
        
        elif choice == "2":
            test_manager.show_database_status()
        
        elif choice == "3":
            novel_id = input("请输入小说ID: ").strip()
            try:
                novel_id = int(novel_id)
                if test_manager.reset_novel_status(novel_id):
                    print("✅ 解析状态重置成功")
                else:
                    print("❌ 重置解析状态失败")
            except ValueError:
                print("❌ 请输入有效的小说ID")
        
        elif choice == "0":
            break
        
        else:
            print("❌ 无效选择，请重新输入")


def handle_step_testing():
    """处理分步骤测试"""
    while True:
        print("\n🧪 分步骤测试")
        print("1. 章节分割测试")
        print("2. 内容清洗测试")
        print("3. 实体提取测试")
        print("4. 关系分析测试")
        print("0. 返回主菜单")
        
        choice = input("\n请选择操作: ").strip()
        
        if choice == "1":
            print("🚀 启动章节分割测试...")
            os.system(f"cd {project_root} && python test/step1_chapter_split.py")
        
        elif choice == "2":
            print("🚀 启动内容清洗测试...")
            os.system(f"cd {project_root} && python test/step2_content_clean.py")
        
        elif choice == "3":
            print("🚧 实体提取测试待实现")
        
        elif choice == "4":
            print("🚧 关系分析测试待实现")
        
        elif choice == "0":
            break
        
        else:
            print("❌ 无效选择，请重新输入")


def handle_data_management():
    """处理数据管理"""
    while True:
        print("\n🗄️ 数据管理")
        print("1. 查看数据库状态")
        print("2. 清理测试数据")
        print("3. 重置所有数据")
        print("0. 返回主菜单")
        
        choice = input("\n请选择操作: ").strip()
        
        if choice == "1":
            test_manager.show_database_status()
        
        elif choice == "2":
            confirm = input("⚠️ 确认清理所有测试数据？(y/N): ").strip().lower()
            if confirm == 'y':
                test_manager.clear_test_data()
            else:
                print("❌ 操作已取消")
        
        elif choice == "3":
            confirm = input("⚠️ 确认重置所有数据？这将清理所有数据！(y/N): ").strip().lower()
            if confirm == 'y':
                test_manager.clear_test_data()
                print("✅ 所有数据已重置")
            else:
                print("❌ 操作已取消")
        
        elif choice == "0":
            break
        
        else:
            print("❌ 无效选择，请重新输入")


def main():
    """主函数"""
    print("🎉 欢迎使用小说解析测试控制台")
    
    try:
        while True:
            show_main_menu()
            choice = input("请选择操作: ").strip()
            
            if choice == "1" or choice.startswith("1."):
                handle_upload_management()
            
            elif choice == "2" or choice.startswith("2."):
                handle_parse_management()
            
            elif choice == "3" or choice.startswith("3."):
                handle_step_testing()
            
            elif choice == "4" or choice.startswith("4."):
                handle_data_management()
            
            elif choice == "5":
                print("👋 感谢使用，再见！")
                break
            
            else:
                print("❌ 无效选择，请重新输入")
    
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，程序退出")
    
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
    
    finally:
        # 清理资源
        test_manager.close_db_session()


if __name__ == "__main__":
    main()
