"""
Agent工厂
实现Agent的统一创建和管理
"""

from typing import Dict, Any, List, Optional, Type
from pathlib import Path
import importlib.util
import inspect
from loguru import logger

from .base import BaseAgent, AgentConfig


class AgentRegistry:
    """Agent注册表"""
    
    def __init__(self):
        self._agents: Dict[str, Type[BaseAgent]] = {}
        self._agent_info: Dict[str, Dict[str, Any]] = {}
    
    def register(self, agent_type: str, agent_class: Type[BaseAgent], info: Dict[str, Any] = None):
        """
        注册Agent类
        
        Args:
            agent_type: Agent类型
            agent_class: Agent类
            info: Agent信息
        """
        self._agents[agent_type] = agent_class
        self._agent_info[agent_type] = info or {}
        logger.debug(f"注册Agent: {agent_type}")
    
    def get_agent_class(self, agent_type: str) -> Optional[Type[BaseAgent]]:
        """获取Agent类"""
        return self._agents.get(agent_type)
    
    def get_available_agents(self) -> List[str]:
        """获取可用的Agent类型列表"""
        return list(self._agents.keys())
    
    def get_agent_info(self, agent_type: str) -> Dict[str, Any]:
        """获取Agent信息"""
        return self._agent_info.get(agent_type, {})


# 全局Agent注册表
_agent_registry = AgentRegistry()


def register_agent(agent_type: str, info: Dict[str, Any] = None):
    """
    Agent注册装饰器
    
    Args:
        agent_type: Agent类型
        info: Agent信息
    """
    def decorator(agent_class: Type[BaseAgent]):
        _agent_registry.register(agent_type, agent_class, info)
        return agent_class
    return decorator


class AgentFactory:
    """
    Agent工厂
    
    负责创建和管理Agent实例
    """
    
    def __init__(self):
        self.registry = _agent_registry
        self._auto_discover_agents()
    
    def _auto_discover_agents(self):
        """自动发现并注册Agent"""
        agents_path = Path(__file__).parent / "agents"

        if not agents_path.exists():
            logger.warning(f"Agents目录不存在: {agents_path}")
            return

        # 遍历agents目录
        for agent_dir in agents_path.iterdir():
            if not agent_dir.is_dir() or agent_dir.name.startswith("__"):
                continue

            # 查找agent.py文件
            agent_file = agent_dir / "agent.py"
            if not agent_file.exists():
                continue

            try:
                # 使用包导入方式而不是文件路径导入
                module_name = f"backend.app.core.llm.agents.{agent_dir.name}.agent"

                try:
                    # 尝试直接导入
                    module = importlib.import_module(module_name)
                except ImportError as import_error:
                    logger.warning(f"无法导入模块 {module_name}: {import_error}")
                    # 如果直接导入失败，尝试文件路径导入（但添加包上下文）
                    spec = importlib.util.spec_from_file_location(
                        module_name, agent_file
                    )
                    if spec is None or spec.loader is None:
                        logger.error(f"无法创建模块规范或加载器: {agent_file}")
                        continue

                    module = importlib.util.module_from_spec(spec)

                    # 设置模块的包上下文
                    module.__package__ = f"backend.app.core.llm.agents.{agent_dir.name}"

                    # 将模块添加到sys.modules中以支持相对导入
                    import sys
                    sys.modules[module_name] = module
                    sys.modules[module.__package__] = module

                    spec.loader.exec_module(module)

                # 查找Agent类
                for name, obj in inspect.getmembers(module):
                    if (inspect.isclass(obj) and
                        issubclass(obj, BaseAgent) and
                        obj != BaseAgent):

                        # 自动注册Agent
                        agent_type = agent_dir.name
                        if agent_type not in self.registry.get_available_agents():
                            self.registry.register(
                                agent_type,
                                obj,
                                {"auto_discovered": True, "path": str(agent_dir)}
                            )
                            logger.info(f"自动发现并注册Agent: {agent_type}")

            except Exception as e:
                logger.error(f"自动发现Agent失败 {agent_dir}: {e}")
                # 添加更详细的错误信息
                import traceback
                logger.debug(f"详细错误信息: {traceback.format_exc()}")
    
    def create_agent(
        self, 
        agent_type: str, 
        config: Optional[AgentConfig] = None,
        **kwargs
    ) -> BaseAgent:
        """
        创建Agent实例
        
        Args:
            agent_type: Agent类型
            config: Agent配置
            **kwargs: 额外参数
            
        Returns:
            Agent实例
        """
        agent_class = self.registry.get_agent_class(agent_type)
        if not agent_class:
            raise ValueError(f"未知的Agent类型: {agent_type}")
        
        # 使用默认配置
        if config is None:
            config = AgentConfig(agent_type=agent_type)
        
        # 创建Agent实例
        try:
            agent = agent_class(config, **kwargs)
            logger.info(f"成功创建Agent: {agent_type}")
            return agent
        except Exception as e:
            logger.error(f"创建Agent失败 {agent_type}: {e}")
            raise
    
    def get_available_agents(self) -> List[str]:
        """获取可用的Agent类型列表"""
        return self.registry.get_available_agents()
    
    def get_agent_info(self, agent_type: str) -> Dict[str, Any]:
        """获取Agent信息"""
        return self.registry.get_agent_info(agent_type)
    
    def list_all_agents(self) -> Dict[str, Dict[str, Any]]:
        """列出所有Agent信息"""
        result = {}
        for agent_type in self.get_available_agents():
            result[agent_type] = self.get_agent_info(agent_type)
        return result


# 全局Agent工厂实例
_agent_factory = AgentFactory()


def create_agent(
    agent_type: str, 
    config: Optional[AgentConfig] = None,
    **kwargs
) -> BaseAgent:
    """
    创建Agent实例（便捷函数）
    
    Args:
        agent_type: Agent类型
        config: Agent配置
        **kwargs: 额外参数
        
    Returns:
        Agent实例
    """
    return _agent_factory.create_agent(agent_type, config, **kwargs)


def get_available_agents() -> List[str]:
    """获取可用的Agent类型列表（便捷函数）"""
    return _agent_factory.get_available_agents()


def get_agent_info(agent_type: str) -> Dict[str, Any]:
    """获取Agent信息（便捷函数）"""
    return _agent_factory.get_agent_info(agent_type)
