#!/usr/bin/env python3
"""
正确的GB2312转UTF-8转换脚本

基于检测结果，将《神秘复苏.txt》从GB2312转换为UTF-8
"""

from pathlib import Path


def convert_gb2312_to_utf8(input_file, output_file=None):
    """
    将GB2312编码的文件转换为UTF-8编码
    
    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径
        
    Returns:
        bool: 转换是否成功
    """
    input_path = Path(input_file)
    
    if not input_path.exists():
        print(f"❌ 文件不存在: {input_file}")
        return False
    
    if output_file is None:
        output_path = input_path.parent / f"{input_path.stem}_utf8{input_path.suffix}"
    else:
        output_path = Path(output_file)
    
    try:
        print(f"📖 读取GB2312文件: {input_path}")
        
        # 使用GB2312编码读取文件
        with open(input_path, 'r', encoding='gb2312', errors='ignore') as f:
            content = f.read()
        
        print(f"✅ 成功读取文件")
        print(f"📊 内容长度: {len(content):,} 字符")
        
        # 统计中文字符
        chinese_chars = sum(1 for char in content if '\u4e00' <= char <= '\u9fff')
        print(f"📊 中文字符: {chinese_chars:,} ({chinese_chars/len(content)*100:.1f}%)")
        
        # 写入UTF-8文件
        print(f"💾 写入UTF-8文件: {output_path}")
        
        with open(output_path, 'w', encoding='utf-8', errors='ignore') as f:
            f.write(content)
        
        print(f"✅ 转换成功!")
        
        # 显示文件大小对比
        original_size = input_path.stat().st_size
        new_size = output_path.stat().st_size
        print(f"📊 文件大小: {original_size:,} bytes → {new_size:,} bytes")
        
        return True
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        return False


def preview_converted_file(file_path, lines=10):
    """
    预览转换后的文件内容
    
    Args:
        file_path: 文件路径
        lines: 预览行数
    """
    try:
        print(f"\n📖 转换后内容预览 (前{lines}行):")
        print("-" * 60)
        
        with open(file_path, 'r', encoding='utf-8') as f:
            line_count = 0
            for line in f:
                line_content = line.strip()
                if line_content:  # 只显示非空行
                    line_count += 1
                    if line_count > lines:
                        break
                    
                    # 限制显示长度
                    display_content = line_content[:80] + "..." if len(line_content) > 80 else line_content
                    print(f"{line_count:2d}: {display_content}")
        
        print("-" * 60)
        
    except Exception as e:
        print(f"❌ 预览失败: {e}")


def verify_conversion(original_file, converted_file):
    """
    验证转换结果
    
    Args:
        original_file: 原文件路径
        converted_file: 转换后文件路径
    """
    try:
        print(f"\n🔍 验证转换结果:")
        
        # 读取原文件
        with open(original_file, 'r', encoding='gb2312', errors='ignore') as f:
            original_content = f.read()
        
        # 读取转换后文件
        with open(converted_file, 'r', encoding='utf-8', errors='ignore') as f:
            converted_content = f.read()
        
        # 比较内容
        if original_content == converted_content:
            print("✅ 内容完全一致，转换成功!")
        else:
            print("⚠️ 内容有差异，可能存在字符丢失")
            
        print(f"原文件字符数: {len(original_content):,}")
        print(f"转换后字符数: {len(converted_content):,}")
        
        # 统计中文字符
        original_chinese = sum(1 for char in original_content if '\u4e00' <= char <= '\u9fff')
        converted_chinese = sum(1 for char in converted_content if '\u4e00' <= char <= '\u9fff')
        
        print(f"原文件中文字符: {original_chinese:,}")
        print(f"转换后中文字符: {converted_chinese:,}")
        
        if original_chinese == converted_chinese:
            print("✅ 中文字符数量一致")
        else:
            print("⚠️ 中文字符数量有差异")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")


def main():
    """主函数"""
    print("🔄 GB2312转UTF-8转换工具")
    print("=" * 60)
    
    input_file = "uploads/神秘复苏.txt"
    output_file = "uploads/神秘复苏_utf8.txt"
    
    if not Path(input_file).exists():
        print(f"❌ 文件不存在: {input_file}")
        return 1
    
    print(f"📁 输入文件: {input_file}")
    print(f"📁 输出文件: {output_file}")
    
    # 执行转换
    success = convert_gb2312_to_utf8(input_file, output_file)
    
    if success:
        # 预览转换后的内容
        preview_converted_file(output_file)
        
        # 验证转换结果
        verify_conversion(input_file, output_file)
        
        print(f"\n🎉 转换完成!")
        print(f"💡 现在您可以在Mac上正常查看文件: {output_file}")
        print(f"💡 可以在测试框架中使用这个UTF-8文件进行测试")
        
        return 0
    else:
        print(f"\n❌ 转换失败")
        return 1


if __name__ == "__main__":
    exit(main())
